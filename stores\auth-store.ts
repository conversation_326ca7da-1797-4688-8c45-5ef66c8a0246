import { useGetPublicUserQuery } from "@/queries/user-queries";
import { Permission, PermissionAction, RoleAdmin, RoleStaff, UserRole } from "@/supabase/types";
import { User } from "@supabase/supabase-js";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface AuthStore {
    token: string;
    expiresAt: number;
    expiresIn: number;
    data: User;

    isAuthenticated(): boolean;
    isAdmin(): boolean;
    isStaff(): boolean;
    role: UserRole;
    permissions: string[];
    getRole(): UserRole;
    getPermissions(): string[];
    hasPermission(action: PermissionAction): boolean;
    setRole(role: UserRole): void;
    setPermissions(permissions: string[]): void;
    setToken(value: string): void;
    setData(data: User): void;
    setExpiresAt(value: number): void;
    setExpiresIn(value: number): void;
    updateUserData(partialUserData: Partial<User>): void;
    refreshUserData(): Promise<void>;
    refreshSession(): Promise<void>;
    logout(): void;
    clearStorage(): void;
}

interface RefreshSessionResponse {
    access_token: string;
    access_token_expires_in: number;
    access_token_expires_at: number;
    user: User;
    error?: string;
}

const useAuthStore = create<AuthStore>()(
    persist(
        (set, get) => {

            return {
                // Access Token (JWT)
                token: "",
                expiresAt: 0,
                expiresIn: 0,
                data: {} as User,
                role: "manager",
                permissions: [],
                // TODO: Add check in API if session is still valid
                isAuthenticated: () => !!get()?.data?.id && !!get().token,
                isAdmin: () => get().getRole() === RoleAdmin,
                isStaff: () => get().getRole() === RoleStaff,
                setData: (data) => set({ data }),
                setToken: (token) => set({ token }),
                setExpiresAt: (expiresAt) => set({ expiresAt }),
                setExpiresIn: (expiresIn) => set({ expiresIn }),
                setRole: (role) => set({ role }),
                setPermissions: (permissions) => set({ permissions }),
                getPermissions: () => get().permissions,
                hasPermission: (action) => {
                    const permissions = get().permissions;

                    // Admin has all permissions
                    if (get().isAdmin()) return true;

                    // Check for exact match
                    if (permissions.includes(action)) return true;

                    // Check for wildcard permissions (e.g., "read:all")
                    const [operation, resource] = action.split(':');
                    if (permissions.includes(`${operation}:all`)) return true;

                    // Check for multi-resource permissions (e.g., "read:products,orders")
                    return permissions.some(perm => {
                        const [permOperation, permResources] = perm.split(':');
                        if (permOperation === operation && permResources) {
                            return permResources.split(',').includes(resource);
                        }
                        return false;
                    });
                },
                updateUserData: (partialUserData) => set((state) => ({
                    data: { ...state.data, ...partialUserData }
                })),
                refreshUserData: async () => {
                    const userId = get().data.id;
                    const token = get().token;

                    if (!userId || !token) return;

                    try {
                        const res = await fetch(`/api/users/${userId}`, {
                            method: "GET",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": `Bearer ${token}`,
                            }
                        });

                        if (!res.ok) return;

                        const response = await res.json();

                        if (response.data) {
                            // Update phone in the auth store if it exists in the response
                            if (response.data.phone && response.data.phone !== get().data.phone) {
                                set((state) => ({
                                    data: { ...state.data, phone: response.data.phone }
                                }));
                            }
                        }
                    } catch (error) {
                        console.error("Failed to refresh user data:", error);
                    }
                },
                // Session must be stored in Cookie
                refreshSession: async () => {
                    const res = await fetch("/api/auth/refresh", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({}),
                    });

                    if (res.status === 403) {
                        // Remove auto data, session has expired
                        get().clearStorage();
                        return;
                    }

                    if (!res.ok) {
                        return;
                    }

                    const refreshTokenResponse: RefreshSessionResponse = await res.json();
                    set({
                        token: refreshTokenResponse.access_token,
                        data: refreshTokenResponse.user,
                        expiresIn: refreshTokenResponse.access_token_expires_in,
                        expiresAt: refreshTokenResponse.access_token_expires_at,
                    });
                },
                getRole: () => {
                    return get().role;
                },
                logout: () => {
                    set(({ token: "", expiresAt: 0, expiresIn: 0, data: {} as User, permissions: [] }));
                    get().clearStorage();
                },
                clearStorage: () => {
                    set(({ token: "", expiresAt: 0, expiresIn: 0, data: {} as User, permissions: [] }));
                    localStorage.removeItem("maxton-auth-data");
                    localStorage.removeItem("maxton-cart-data");
                }
            }
        },
        {
            name: "maxton-auth-data",
            storage: createJSONStorage(() => localStorage)
        }
    )
);

export default useAuthStore;