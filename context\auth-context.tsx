import dynamic from "next/dynamic";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";
import { createContext, useEffect } from "react";
import useAuthStore from "stores/auth-store";

const PUBLIC_ROUTES = [
  "/",
  "/log-in",
  "/sign-up",
  "/forgot-password",
  "/auth/callback",
  "/store/products",
  "/store/checkout",
  "/store/order-confirmation",
];

const PROTECTED_ROUTES = [
  "/store/dashboard",
  "/store/orders",
  "/store/customers",
  "/store/account",
];

const ADMIN_ROUTES = [
  "/admin/dashboard",
  "/admin/orders",
  "/admin/orders/cancellations",
  "/admin/customers",
  "/admin/customers/pending-billing",
  "/admin/customers/pending",
  "/admin/customers/rejected",
  "/admin/customers/groups",
  "/admin/products",
  "/admin/products/new",
  "/admin/products/[id]/edit",
  "/admin/products/categories",
  "/admin/products/arrangements",
  "/admin/reports",
  "/admin/reports/sales",
  "/admin/reports/sales-by-month",
  "/admin/reports/sales-by-week",
  "/admin/requests",
  "/admin/groups",
  "/admin/categories",
  "/admin/settings",
  "/admin/settings/tax-rates",
  "/admin/settings/users",
  "/admin/products/bulk-price-update",
];

const AUTH_ROUTES = [
  "/log-in",
  "/sign-up",
  "/forgot-password",
  "/auth/callback",
];

const ALLOWED_STORE_ROUTES_FOR_ADMIN = [
  "/store/products",
  "/store/orders",
  "/store/order-confirmation",
  "/store/checkout",
];

function handleRedirects(
  pathname: string,
  isAuthenticated: boolean,
  isAdminOrStaff: boolean
) {
  // Unauthenticated users trying to access protected or admin routes
  const isProtectedOrAdminRoute = PROTECTED_ROUTES.includes(pathname) ||
    ADMIN_ROUTES.includes(pathname) ||
    (pathname.startsWith("/admin/products") && pathname.endsWith("/edit"));

  if (!isAuthenticated && isProtectedOrAdminRoute) {
    return "/log-in";
  }

  // Auth routes redirect based on role
  if (AUTH_ROUTES.includes(pathname) && isAuthenticated && isAdminOrStaff) {
    return "/admin/dashboard";
  }

  if (AUTH_ROUTES.includes(pathname) && isAuthenticated && !isAdminOrStaff) {
    return "/store/dashboard";
  }

  // Admin accessing store dashboard
  if (pathname === "/store/dashboard" && isAuthenticated && isAdminOrStaff) {
    return "/admin/dashboard";
  }

  // Admin accessing protected store routes that aren't in allowed list
  const isRestrictedStoreRoute = pathname.startsWith("/store") &&
    PROTECTED_ROUTES.includes(pathname) &&
    !ALLOWED_STORE_ROUTES_FOR_ADMIN.some(route => pathname.includes(route));

  if (isRestrictedStoreRoute && isAuthenticated && isAdminOrStaff) {
    return "/admin/dashboard";
  }

  // Regular user accessing admin routes
  const isAdminRoute = ADMIN_ROUTES.includes(pathname) ||
    (pathname.startsWith("/admin/products") && pathname.endsWith("/edit"));

  if (isAdminRoute && isAuthenticated && !isAdminOrStaff) {
    return "/store/dashboard";
  }

  return null;
}

interface AuthContextType {
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

function AuthProvider({ children }: Readonly<{ children: React.ReactNode }>) {
  const isAdmin = useAuthStore((state) => state.isAdmin)();
  const isStaff = useAuthStore((state) => state.isStaff)();
  const isAdminOrStaff = isAdmin || isStaff;

  const router = useRouter();
  const query = useSearchParams();
  const redirectTo = query?.get("redirect");
  
  // Validate if the redirect URL is safe (internal to the application)
  const isValidRedirectUrl = (url: string | null): boolean => {
    if (!url) return false;
    
    // Check if it's a relative path (starts with /)
    if (url.startsWith('/')) {
      // Prevent path traversal attacks
      return !url.includes('..') && !url.includes('//');
    }
    
    try {
      // Check if it's on the same domain
      const urlObj = new URL(url);
      const hostname = window.location.hostname;
      return urlObj.hostname === hostname || 
             urlObj.hostname.endsWith(`.${hostname}`);
    } catch {
      // Invalid URL format
      return false;
    }
  };

  const pathname = router.pathname;
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const accessTokenExpiry = useAuthStore((state) => state.expiresAt);
  const refreshSession = useAuthStore((state) => state.refreshSession);

  // Handle redirects in a single useEffect
  useEffect(() => {
    // Don't do anything if pathname is not available yet
    if (!pathname) return;

    // Handle custom redirect from query params first
    if (isAuthenticated() && redirectTo && isValidRedirectUrl(redirectTo)) {
      router.push(redirectTo);
      return;
    }

    const redirectPath = handleRedirects(pathname, isAuthenticated(), isAdminOrStaff);

    if (redirectPath) {
      router.replace(redirectPath);
    }
  }, [isAuthenticated(), isAdmin, isStaff, pathname, redirectTo]);

  // Refresh session if access token is expired
  useEffect(
    function validateAccessToken() {
      if (!pathname || !isAuthenticated() || !accessTokenExpiry) {
        return;
      }

      const calculateRefreshTime = () => {
        const currentTime = Date.now();
        const expirationTime = accessTokenExpiry * 1000;
        const bufferTime = 60000; // 1 minute buffer

        return Math.max(expirationTime - currentTime - bufferTime, 0);
      };

      const refreshTime = calculateRefreshTime();
      console.log("Refresh time:", refreshTime);

      const refreshTimer = setTimeout(() => {
        console.log("Refreshing session...");
        refreshSession();
      }, refreshTime);

      return () => clearTimeout(refreshTimer);
    },
    [isAuthenticated(), accessTokenExpiry, refreshSession, pathname]
  );

  return (
    <AuthContext.Provider value={{ isAuthenticated: isAuthenticated() }}>
      {children}
    </AuthContext.Provider>
  );
}

export const AuthContextProvider = dynamic(
  () => Promise.resolve(AuthProvider),
  {
    ssr: false,
  }
);
