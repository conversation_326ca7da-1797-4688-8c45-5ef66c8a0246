/** @type {import("next").NextConfig} */

const nextConfig = {
  i18n: {
    // internalized routing
    locales: ["en"],
    defaultLocale: "en",
  },
  images: {
    remotePatterns: [
      { hostname: "cdn.sanity.io" },
      { hostname: "source.unsplash.com" },
      { hostname: "**.cdninstagram.com" },
    ],
  },
  typescript: {
    // Set this to false if you want production builds to abort if there's type errors
    ignoreBuildErrors: true,
  },
  eslint: {
    // Set this to false if you want production builds to abort if there's lint errors
    ignoreDuringBuilds: true,
  },
  async redirects() {
    return [
      {
        source: "/home",
        destination: "/",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/MCP-Tasks-Procedures-Maxton-Valves.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/bde96a42826d29961fa510de7c0ea2a165361a01.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/engineering.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/c2508ac54c805ac43a54c386dd46f66553207745.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/EMV10-10T-User-Manual.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/0d22ace3d270b940d2103ad756e6c36884417874.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC4-4M-Exploded-View.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/1218f0c5717f35c82de7e3f87c73283dc69476c3.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC4MR-Exploded-View.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/f2eebf742a2cf16f4b09023844c8af86e5c42911.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC1-1A-Exploded-View.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/dd20a5f6aebd15e591a3ca75fde3fad5f5c231ab.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC2-UC2A-Exploded-View.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/3087a53a73c9b6491f50751069074803bece37be.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC1-1A-2-2A-Trouble-shooting.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/9bcdd58e919ff90f304f5557fb15b8ff79da7d86.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC4-4M-4MR-Trouble-shooting.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/dc460bc85a196a2e1734b480f6906b1347873935.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC4_4MAP.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/5d8500f680d026530f3148e78aadcf2722d60a1f.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC4MRAP.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/fcd27effc2a3502811b52fa51280ed8b898d18ab.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/UC1A_2A_Adjust_Proc.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/e0acf7115d825a6088e9e8399cd991d07362bec0.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/EMV10-10T-User-Manual.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/0d22ace3d270b940d2103ad756e6c36884417874.pdf",
        permanent: true,
      },
      {
        source:
          "/wp-content/uploads/DualHydraulicControllerAdjustmentProcedure.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/a058fad84cfa1983b7272a0ac5bb88ac8fac2865.pdf",
        permanent: true,
      },
      {
        source: "/wp-content/uploads/OSV_hydraulic.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/90f1f980911e332ecf92df553f6db920bb524608.pdf",
        permanent: true,
      },
      {
        source:
          "/wp-content/uploads/DualOSVHydraulicControllerAdjustmentprocedure.pdf",
        destination:
          "https://cdn.sanity.io/files/s0dti8ee/production/09c5ddf4380b283fae26213a0792f62572697600.pdf",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/product" },
          { type: "query", key: "path", value: "65" },
          { type: "query", key: "product_id", value: "190" },
        ],
        destination: "/store/products/HPAS-1",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/product" },
          { type: "query", key: "path", value: "65" },
          { type: "query", key: "product_id", value: "108" },
          { type: "query", key: "utm_source", value: "EW" },
          { type: "query", key: "utm_medium", value: "mag" },
          { type: "query", key: "utm_campaign", value: "Nov 23" },
        ],
        destination: "/store/products/MAXGLIDE",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/product" },
          { type: "query", key: "product_id", value: "185" },
          { type: "query", key: "utm_source", value: "EW" },
          { type: "query", key: "utm_medium", value: "Ad" },
          { type: "query", key: "utm_campaign", value: "Sept 23 NAEC edition" },
        ],
        destination: "/store/products/SAFETACH2",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/product" },
          { type: "query", key: "path", value: "59_75" },
          { type: "query", key: "product_id", value: "162" },
          { type: "query", key: "utm_source", value: "EW" },
          { type: "query", key: "utm_medium", value: "Classified ad" },
          { type: "query", key: "utm_campaign", value: "August" },
        ],
        destination: "/store/products/ILV",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/category" },
          { type: "query", key: "path", value: "59_69" },
          { type: "query", key: "utm_source", value: "EW" },
          { type: "query", key: "utm_medium", value: "classified ad" },
          { type: "query", key: "utm_campaign", value: "August" },
        ],
        destination:
          "/store/products?category=Overspeed+%2F+Pipe+Rupture+Valves+-+Commercial&page=1",
        permanent: true,
      },
      {
        source: "/cet-classes",
        has: [
          { type: "query", key: "utm_source", value: "EW" },
          { type: "query", key: "utm_medium", value: "Trade Mag" },
          { type: "query", key: "utm_campaign", value: "June Issue" },
        ],
        destination: "/cet-cat-classes",
        permanent: true,
      },
      {
        source: "/cet-classes",
        has: [
          { type: "query", key: "utm_source", value: "EW" },
          { type: "query", key: "utm_medium", value: "Trade Mag" },
          { type: "query", key: "utm_campaign", value: "May Issue" },
        ],
        destination: "/cet-cat-classes",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/category" },
          { type: "query", key: "path", value: "59_68" },
          { type: "query", key: "utm_source", value: "EW May 23 Issue" },
          { type: "query", key: "utm_medium", value: "Display ad" },
          { type: "query", key: "utm_campaign", value: "Valves" },
        ],
        destination: `/store/products?category=Hydraulic+Elevator+Control+Valves+-+Commercial`,
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/category" },
          { type: "query", key: "path", value: "60" },
          { type: "query", key: "utm_source", value: "EW" },
          { type: "query", key: "utm_medium", value: "Class Ad" },
          { type: "query", key: "utm_campaign", value: "23'" },
        ],
        destination: "/store/products?category=Isolation+Couplings&page=1",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/category" },
          { type: "query", key: "path", value: "59_69" },
        ],
        destination:
          "/store/products?category=Overspeed+%2F+Pipe+Rupture+Valves+-+Commercial&page=1",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/category" },
          { type: "query", key: "path", value: "59_74" },
        ],
        destination:
          "/store/products?category=Overspeed+%2F+Pipe+Rupture+-+Residential%2FLULA&page=1",
        permanent: true,
      },
      {
        source: "/products/index.php",
        has: [
          { type: "query", key: "route", value: "product/category" },
          { type: "query", key: "path", value: "78" },
        ],
        destination: "/store/products?category=Elevator+Shut+off+Valves&page=1",
        permanent: true,
      },
    ];
  },
  async rewrites() {
    return [{ source: "/sitemap.xml", destination: "/api/sitemap" }];
  },
  compiler: {
    // Remove console logs only in production
    removeConsole: process.env.NODE_ENV === "production",
  },
};

export default nextConfig;
